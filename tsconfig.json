{"compilerOptions": {"module": "commonjs", "declaration": true, "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "target": "ES2021", "sourceMap": true, "outDir": "./dist", "baseUrl": "./", "incremental": true, "forceConsistentCasingInFileNames": true, "skipLibCheck": true, "strictNullChecks": true, "noImplicitAny": true, "strictBindCallApply": false, "esModuleInterop": true, "noFallthroughCasesInSwitch": false, "strict": true}, "files": ["src/main.ts"], "include": ["src/types/builtInOverrides.d.ts", "src/**/*.test.ts"]}