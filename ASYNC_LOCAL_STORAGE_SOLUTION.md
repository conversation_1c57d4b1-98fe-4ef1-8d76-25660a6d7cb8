# AsyncLocalStorage Memory Leak Fix - Implementation Guide

## Problem Analysis

The original implementation had several critical issues causing memory leaks:

1. **Missing AsyncLocalStorage Definition**: `LoggerService.requestStorage` was referenced but never defined
2. **Improper Context Management**: Using `enterWith()` without proper cleanup
3. **No Response-Level Cleanup**: Context persisted beyond request lifecycle
4. **Validation Errors Not Captured**: JoiParamPipe used global logger instead of request context

## Solution Architecture

### 1. RequestContextService (`src/context/request-context.service.ts`)
- **Purpose**: Centralized AsyncLocalStorage management with proper cleanup
- **Key Features**:
  - Uses `AsyncLocalStorage.run()` for automatic memory management
  - Provides clean API for context operations
  - Handles validation error collection
  - Safe fallbacks when no context exists

### 2. RequestContextMiddleware (`src/middleware/debug-logger.middleware.ts`)
- **Purpose**: Initialize request context at the start of each request
- **Key Features**:
  - Extracts `isDebug` parameter from query string
  - Creates request-scoped context using `runWithContext()`
  - Automatic cleanup when request processing completes

### 3. DebugResponseInterceptor (`src/interceptors/debug-response.interceptor.ts`)
- **Purpose**: Add debug headers and ensure cleanup at response time
- **Key Features**:
  - Adds `x-tvn-debug-400` header when validation errors exist
  - Works for both successful and error responses
  - Maintains existing header format: `"bad value: $problematicParameterNames"`

### 4. Enhanced JoiParamPipe (`src/components/ad/joi-param.pipe.ts`)
- **Purpose**: Capture validation errors in request context
- **Key Features**:
  - Maintains existing error logging functionality
  - Captures parameter names for debug headers when in debug mode
  - Uses request context instead of global state

## Memory Leak Prevention

### Root Cause Fixes:
1. **Proper AsyncLocalStorage Usage**: 
   - ✅ Uses `run(context, callback)` instead of `enterWith()`
   - ✅ Automatic cleanup when callback completes
   - ✅ No manual context references stored outside request scope

2. **Request Lifecycle Management**:
   - ✅ Context initialized at request start
   - ✅ Context automatically cleaned up at request end
   - ✅ Interceptor ensures cleanup even on errors

3. **Safe Context Access**:
   - ✅ All context methods handle undefined context gracefully
   - ✅ No memory references persist beyond request lifecycle
   - ✅ Validation errors only collected in debug mode

## Usage Example

```typescript
// The system works automatically, but here's how it flows:

// 1. Request comes in with ?isDebug=true
// 2. RequestContextMiddleware initializes context:
RequestContextService.runWithContext({
  requestId: 'uuid-123',
  isDebugMode: true,
  validationErrors: []
}, () => {
  // 3. Request processing happens within this context
  // 4. JoiParamPipe captures validation errors
  // 5. DebugResponseInterceptor adds headers
  // 6. Context automatically cleaned up when callback ends
});
```

## Integration Points

### Controller Changes:
- ✅ Removed manual debug header logic
- ✅ Added `@UseInterceptors(DebugResponseInterceptor)`
- ✅ Simplified method signature (removed req/res parameters)

### Module Configuration:
- ✅ Added `ContextModule` to imports
- ✅ Updated middleware registration to use `RequestContextMiddleware`

## Testing

The implementation includes comprehensive tests that verify:
- ✅ Context isolation between requests
- ✅ Automatic cleanup after request completion
- ✅ Validation error collection in debug mode
- ✅ Safe behavior when no context exists

## Backward Compatibility

The solution maintains backward compatibility:
- ✅ Existing debug header format preserved
- ✅ LoggerService continues to work for non-debug scenarios
- ✅ All existing functionality maintained

## Performance Impact

- ✅ Minimal overhead - context only created when needed
- ✅ No memory leaks - automatic cleanup via `run()` method
- ✅ No global state pollution
- ✅ Efficient validation error collection

## Deployment Notes

1. The solution is ready for production use
2. No database migrations or configuration changes required
3. Existing debug functionality will work immediately
4. Memory usage should decrease due to proper cleanup
