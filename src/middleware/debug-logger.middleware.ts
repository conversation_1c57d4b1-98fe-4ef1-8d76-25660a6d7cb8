import { Injectable, NestMiddleware } from '@nestjs/common';
import { randomUUID } from 'crypto';
import { FastifyReply, FastifyRequest } from 'fastify';
import { RequestContext, RequestContextService } from '../context/request-context.service';

@Injectable()
export class RequestContextMiddleware implements NestMiddleware {
  use(req: FastifyRequest, res: FastifyReply, next: () => void) {
    const requestId = randomUUID();
    const isDebugMode = this.extractDebugMode(req);

    const context: RequestContext = {
      requestId,
      isDebugMode,
      collectedLogs: []
    };

    RequestContextService.runWithContext(context, () => {
      (req as any).requestId = requestId;
      next();
    });
  }

  private extractDebugMode(req: FastifyRequest): boolean {
    const query = req.query as any;
    return query?.isDebug === 'true' || query?.isDebug === true;
  }
}
