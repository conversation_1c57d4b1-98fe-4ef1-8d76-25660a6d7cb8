import { Injectable, NestMiddleware } from '@nestjs/common';
import { randomUUID } from 'crypto';
import { FastifyReply, FastifyRequest } from 'fastify';
import { RequestContext, RequestContextService } from '../context/request-context.service';

@Injectable()
export class RequestContextMiddleware implements NestMiddleware {
  use(req: FastifyRequest, res: FastifyReply, next: () => void) {
    const requestId = randomUUID();
    const isDebugMode = this.extractDebugMode(req);

    const context: RequestContext = {
      requestId,
      isDebugMode,
      collectedLogs: []
    };

    // Store context in cache and set current request ID
    RequestContextService.setContext(context);
    RequestContextService.setCurrentRequestId(requestId);

    // Store request ID in request object for access in other parts of the application
    (req as any).requestId = requestId;

    next();
  }

  private extractDebugMode(req: FastifyRequest): boolean {
    const query = req.query as any;
    return query?.isDebug === 'true' || query?.isDebug === true;
  }
}
