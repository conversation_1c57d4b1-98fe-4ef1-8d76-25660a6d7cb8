import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { Injectable } from '@nestjs/common';
import { LoggerService } from '../logger/logger.service';
import { xmlParser } from '../utils/xmlParser';

type Stringifyable = { toString(): string };

@Injectable()
export class HttpService {
  private axiosInstance: AxiosInstance;

  constructor(private logger: LoggerService) {
    this.axiosInstance = axios.create();
  }

  async getJsonResponse<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
    this.logger.debug('HTTP_REQUEST', { url, config });
    const response: AxiosResponse<Stringifyable> = await this.axiosInstance.get(url, config);

    const resData = response.data;
    const stringResponse = resData.toString();

    const jsonResult = xmlParser.fromXMLtoJSON(stringResponse);
    // this.logger.debug('HTTP_RESPONSE', jsonResult );

    return jsonResult as T;
  }
}
