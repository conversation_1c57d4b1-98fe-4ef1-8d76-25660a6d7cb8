import { Inject, Injectable } from '@nestjs/common';
import { CACHE_MANAGER, Cache } from '@nestjs/cache-manager';
import { LoggerService } from '../logger/logger.service';

const TTL = 10 * 1000;

@Injectable()
export class LocalCacheService {
  constructor(
    @Inject(CACHE_MANAGER) private readonly cacheManager: Cache,
    private logger: LoggerService
  ) {
    this.logger.setContext(LocalCacheService.name);
  }

  /**
   * Retrieves the value associated with the given key from the cache.
   *
   * @param {T} key - The key to retrieve the value for. Must be either 'ch' for Channel or 'v' for Version.
   * @return {Promise<ChannelOrVersion<T> | undefined>}
   * A promise that resolves to the value associated with the key, or undefined if not found.
   */
  public async get<T>(key: string): Promise<T | null> {
    const result: T | null = await this.cacheManager.get<Awaited<T>>(key);
    this.logger.log('GET_FROM_LOCAL_CACHE', { key, result });
    return result;
  }

  public async set<D>(key: string, data: D, ttl: number = TTL): Promise<void> {
    await this.cacheManager.set(key, data, ttl);
    this.logger.log('SET_TO_LOCAL_CACHE', { key, data });
  }

  public async del(key: string): Promise<void> {
    await this.cacheManager.del(key);
    this.logger.log('DEL_FROM_LOCAL_CACHE', { key });
  }
}
