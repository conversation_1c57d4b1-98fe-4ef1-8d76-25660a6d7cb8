import * as dotenv from 'dotenv';
import { cleanEnv, port, str, host, num, bool } from 'envalid';
import { between0AndNumCPUs } from '../utils';

dotenv.config();

export const env = cleanEnv(process.env, {
  APP_ENV: str({ devDefault: 'local', default: 'prod' }),
  APP_NAME: str({ default: 'herring' }),
  SLAVE_COUNT_REDUCE: between0AndNumCPUs({ default: 4 }),

  APP_PORT: port({ default: 4002 }),
  APP_PORT_SLAVE: port({ default: 4001 }),
  APP_ADDRESS: host({ default: '127.0.0.1' }),

  // CACHE
  CACHE_MAX_SIZE: num({ default: 4000 }),

  // REDIS
  REDIS_HOST: host({
    default: ''
  }),
  REDIS_PORT: port({ default: 6379 }),
  REDIS_PASS: str({ default: '' }),
  REDIS_PROVIDE_PASS: bool({ default: false }),
  REDIS_DB_ID: num({ default: 6 }),

  //LOGS
  DEBUG_MODE: str({
    desc: 'Debug Logs: to be or not to be',
    default: 'DISABLED',
    devDefault: 'ENABLED',
    choices: ['ENABLED', 'DISABLED']
  }),

  AD_SERVER_URL: host({
    desc: 'Redirect URL for ad requests',
    default: 'https://tvn.adocean.pl/ad.xml/'
  })
});
