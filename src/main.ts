import { NestFactory } from '@nestjs/core';
import { FastifyAdapter, NestFastifyApplication } from '@nestjs/platform-fastify';
import { env } from './env/envalidConfig';
import { MasterModule, SlaveModule } from './app/app.module';
import { LoggerService } from './logger/logger.service';
import * as os from 'os';
import helmet from '@fastify/helmet';
import fastifyCompress from '@fastify/compress';
import fastifyCors from '@fastify/cors';
import qs from 'qs';
import cluster from 'cluster';

const delimiter = env.APP_ENV === 'local' ? /\/|&/ : '/';

async function bootstrap() {
  const context = cluster.isWorker ? `SLAVE ${cluster.worker?.id}` : `MASTER`;
  const logger = new LoggerService();
  logger.setContext(`Bootstrap ${context}`);

  if (cluster.isPrimary) {
    try {
      if (env.APP_ENV === 'local') {
        logger.debug('LOCAL_ENV', { delimiter, env });
      }

      logger.log('CLUSTER_INIT_PRIMARY_WORKER');
      const numCPUs = env.APP_ENV === 'local' ? 1 : os.cpus().length - env.SLAVE_COUNT_REDUCE;
      for (let i = 0; i < numCPUs; i++) {
        logger.log('CLUSTER_FORK');
        cluster.fork();
      }

      cluster.on('exit', () => {
        logger.warn('CLUSTER_EXIT');
        cluster.fork();
      });

      const masterFastifyAdapter = new FastifyAdapter({
        ignoreTrailingSlash: true,
        querystringParser: (str) => qs.parse(str, { delimiter })
      });
      const masterApp = await NestFactory.create<NestFastifyApplication>(
        MasterModule,
        masterFastifyAdapter,
        {
          logger,
          bufferLogs: true,
          abortOnError: true
        }
      );
      await masterApp.register(helmet);
      await masterApp.register(fastifyCompress);
      await masterApp.register(fastifyCors, {
        origin: true,
        credentials: true,
        preflight: false
      });

      const port = env.APP_PORT;
      logger.log('MASTER_PROCESS_STARTED', { port });

      await masterApp.listen({ port, host: env.APP_ADDRESS });
    } catch (e: any) {
      logger.fatal('GENERIC', e?.stack || e);
    }
  } else if (cluster.isWorker) {
    try {
      const port = env.APP_PORT_SLAVE;
      logger.log('CLUSTER_INIT_SLAVE_WORKER', { port, id: cluster.worker?.id });

      const appFastifyAdapter = new FastifyAdapter({
        ignoreTrailingSlash: true,
        querystringParser: (str) => qs.parse(str, { delimiter })
      });

      const slaveApp = await NestFactory.create<NestFastifyApplication>(
        SlaveModule,
        appFastifyAdapter,
        {
          logger,
          bufferLogs: true,
          abortOnError: true
        }
      );

      await slaveApp.register(helmet);
      await slaveApp.register(fastifyCompress);
      await slaveApp.register(fastifyCors, {
        origin: true,
        credentials: true,
        preflight: false
      });

      logger.log('CLUSTER_INIT_SLAVE_APP_INIT', {
        port,
        host: env.APP_ADDRESS,
        id: cluster.worker?.id
      });

      await slaveApp.listen({ port: env.APP_PORT_SLAVE, host: env.APP_ADDRESS });
    } catch (e: any) {
      logger.fatal('GENERIC', e?.stack || e);
    }
  }
}
bootstrap();
