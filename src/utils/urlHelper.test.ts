import { URLParamsHelper } from './urlHelper';

describe('URLParamHelper tests ', () => {
  const query =
    'aocodetype=1/id=A1w3VxpUJ7UcI7sa6HfkBq7OAyJJ31IAAF6sVrSP2LS7/p=1/bid=7515456263876321/mid1dur=15/mid1maxdur=15/mid1mindur=15/';

  const baseUrl = 'https://tvn.adocean.pl/ad.xml';

  const url = `${baseUrl}?${query}`;
  it('should remove id param from url', () => {
    const helper = new URLParamsHelper(url);
    helper.delete('id');
    expect(helper.toString()).toEqual(
      'https://tvn.adocean.pl/ad.xml?aocodetype=1/p=1/bid=7515456263876321/mid1dur=15/mid1maxdur=15/mid1mindur=15/'
    );
  });

  it('should remove all of the specified params', () => {
    const helper = new URLParamsHelper(url);
    helper
      .delete('id')
      .delete('p')
      .delete('bid')
      .delete('mid1dur')
      .delete('mid1maxdur')
      .delete('mid1mindur')
      .delete('aocodetype');
    expect(helper.toString()).toEqual('https://tvn.adocean.pl/ad.xml');
  });

  it('should not remove existing params if invoked params do not exist', () => {
    const helper = new URLParamsHelper(url);
    helper.delete('nonExistingParam');
    expect(helper.toString()).toEqual(url);
  });

  it('should not modify non existing params', () => {
    const helper = new URLParamsHelper(url);
    helper.set('nonExistingParam', 'value');
    expect(helper.toString()).toEqual(url);
  });

  it('should replace specified params with given values', () => {
    const helper = new URLParamsHelper(url);
    helper.set('id', 'abc').set('p', '2').set('bid', '1234').set('mid1dur', '30');
    expect(helper.toString()).toEqual(
      'https://tvn.adocean.pl/ad.xml?aocodetype=1/id=abc/p=2/bid=1234/mid1dur=30/mid1maxdur=15/mid1mindur=15/'
    );
  });

  it('should be able to mix functions', () => {
    const helper = new URLParamsHelper(url);
    helper
      .delete('id')
      .delete('bid')
      .set('p', '2')
      .set('mid1dur', '30')
      .add('test1', '1')
      .add('test2', '2');
    expect(helper.toString()).toEqual(
      'https://tvn.adocean.pl/ad.xml?aocodetype=1/p=2/mid1dur=30/mid1maxdur=15/mid1mindur=15/test1=1/test2=2/'
    );
  });

  it('should be able to add params', () => {
    const helper = new URLParamsHelper(url);
    helper.add('test1', '1').add('test2', '2').add('test3', '3');
    expect(helper.toString()).toEqual(`${url}test1=1/test2=2/test3=3/`);
  });

  it('should be able to add boolean, number, string param', () => {
    const helper = new URLParamsHelper(url);
    helper.add('test1', true).add('test2', 2).add('test3', '3');
    expect(helper.toString()).toEqual(`${url}test1=true/test2=2/test3=3/`);
  });

  it('should be able to add encoded param', () => {
    const helper = new URLParamsHelper(url);
    helper.add('custParams', '1', true);
    expect(helper.toString()).toEqual(`${url}custParams=1/`);
  });

  it('should be able to add long encoded param', () => {
    const helper = new URLParamsHelper(url);
    helper.add('custParams', 'adid=123&o=123&ct=linear&ch=TTV', true);
    expect(helper.toString()).toEqual(
      `${url}custParams=adid%3D123%26o%3D123%26ct%3Dlinear%26ch%3DTTV/`
    );
  });

  it('should be able handle only encoded params with & as separator', () => {
    const helper = new URLParamsHelper('adid%3D123%26o%3D123%26ct%3Dlinear%26ch%3DTTV');
    helper.add('a', '1');
    expect(helper.toString()).toEqual('adid%3D123%26o%3D123%26ct%3Dlinear%26ch%3DTTV%26a%3D1');
  });

  it('should be able handle only encoded params with / as separator', () => {
    const helper = new URLParamsHelper('adid%3D123%2Fo%3D123%2Fct%3Dlinear%2Fch%3DTTV');
    helper.add('a', '1');
    expect(helper.toString()).toEqual('adid%3D123%2Fo%3D123%2Fct%3Dlinear%2Fch%3DTTV%2Fa%3D1');
  });

  it('should be able handle only decoded params', () => {
    const helper = new URLParamsHelper('adid=123&o=123&ct=linear&ch=TTV');
    helper.add('a', '1');
    expect(helper.toString()).toEqual('adid=123&o=123&ct=linear&ch=TTV&a=1');
  });

  it('should be able handle only one decoded param', () => {
    const helper = new URLParamsHelper('adid=123');
    helper.add('a', '1');
    expect(helper.toString()).toEqual('adid=123/a=1');
  });

  it('should be able handle only one encoded param', () => {
    const helper = new URLParamsHelper('adid%3D123');
    helper.add('a', '1');
    expect(helper.toString()).toEqual('adid%3D123%2Fa%3D1');
  });

  it('should add question mark if params did not exist', () => {
    const helper = new URLParamsHelper('https://tvn.adocean.pl/ad.xml');
    helper.add('a', '1');
    expect(helper.toString()).toEqual(`https://tvn.adocean.pl/ad.xml?a=1`);
  });

  it('should be able to start from empty string and buildup query', () => {
    const helper = new URLParamsHelper('');
    helper.add('a', '1').add('b', '2').add('c', '3');
    expect(helper.toString()).toEqual('a=1/b=2/c=3');
  });

  it('should be able to start from empty string and buildup encoded query', () => {
    const helper = new URLParamsHelper('', '%26');
    helper.add('a', '1').add('b', '2').add('c', '3');
    expect(helper.toString()).toEqual('a%3D1%26b%3D2%26c%3D3');
  });

  it('should check before adding parameters', () => {
    const helper = new URLParamsHelper(url);
    helper.addMaybe('test1', '1').addMaybe('test2', '').addMaybe('test3', undefined);
    expect(helper.toString()).toEqual(`${url}test1=1/`);
  });

  it('should be able to start from decoded query, and force it be encoded because of the separator', () => {
    const helper = new URLParamsHelper('z=20&y=10', '%26');
    helper.add('a', '1');
    expect(helper.toString()).toEqual('z%3D20%26y%3D10%26a%3D1');
  });

  it('should be able to start no input url', () => {
    const helper = new URLParamsHelper();
    helper.add('a', '1');
    expect(helper.toString()).toEqual('a=1');
  });

  it('should be able to start from encoded url without query', () => {
    const helper = new URLParamsHelper(encodeURIComponent(baseUrl));
    helper.add('a', '1');
    expect(helper.toString()).toEqual(encodeURIComponent(`${baseUrl}?a=1`));
  });

  it('should be able to start from encoded url with query', () => {
    const helper = new URLParamsHelper(encodeURIComponent(url));
    helper.add('a', '1');
    expect(helper.toString()).toEqual(encodeURIComponent(`${url}a=1/`));
  });

  it('should keep parameters with this same name', () => {
    const fwUrl =
      'http://5e529.v.fwmrm.net/ad/g/1?prof=386345%3ADNI_IT_HbbTV_SSAI_live&nw=386345&metr=1031&caid=ADDRESSABLE_TV_DUMMY_ASSET&asnw=386345&csid=ADDRESSABLE_TV_ITALY_MTIT&vprn=77399033&vrdu=30&vip=************&vdur=3600&resp=vmap1%2Bvast4&flag=+scpv+emcr+amcb+slcb+aeti;_fw_vcid2=&test=mxf&ch=MTIT&pod=20250205MTIT000000206&p=6&adid=ITA-96178&wbd_ad_dur=30&wbd_ad_dur=na&_fw_h_user_agent=Mozilla%2F5.0%20(Macintosh%3B%20Intel%20Mac%20OS%20X%2010_15_7)%20AppleWebKit%2F537.36%20(KHTML%2C%20like%20Gecko)%20Chrome%2F96.0.4664.110%20Safari%2F537.36;ptgt=a&_fw_gdpr=1&_fw_gdpr_consent=CP5WVhgP5WVhgAcABBENAlEwAP_AAEAAACiQJQMD_DJ8BSNDYWJ1IIs0aAUXwRABxkQhAgLBAwABiBKAOIQAkCAAAAEANCAAAAIAMDJAAAFADIAAAAAAAAgAIAAMIAAQAABKAABAAAAAAAAQCAgAAABAAQAQgmAEAAcAgAAlgAIoAFAAAAFCAACBAAAAEAAFAgkAAAAAAAAIAAAIICyABQAAhAAAAAAAABgQAAAAAAAEEK4FUACgALAAcABUADgAHgAQAAyABoADwAIgATAApABVAC2AGYAN4AegA_ACEAEcAJoATgAwwBlADRAHOAO4AfoA_wCEAEdAL6Ae0BIgCdgFDgLYAXQAvMB-4EGAIVgEDoEIACwAKgAcABBADEANAAeABEACYAFUAMQAZgA3gB6AD8AJoATgAwwBlADRAHOAO4AfoA_wCLAEdAReAkQBOwChwFsALzAZYBBgcAKgAcAB4APwAoABoAEcAQgBdADBAH7gQrIQDAAFgAxACYAFUAMQAbwA9ACOAHOAO4Af4EGCAAUAB4AaAC-gMEAhWSgHAALAA4ADEAHgARAAmABVADEAI4BF4CRAFsALzAgwSABgDuAZYA_cpAcAAWABUADgAIIAYgBoADwAIgATAApABVADEAGYAPwAygBogDnAH6ARYAjoB7QEXgJEATsAocBbAC8wGWAQYKACgBHACcAHcAXUA_4CpAF0AMEAfuBCsAAA.f_gACAAAAAAA&mind=30&maxd=30&tpos=0&slau=Preroll%20Spot&inventory=commercial_break';

    const helper = new URLParamsHelper(fwUrl);

    expect(helper.toString()).toEqual(fwUrl);
  });
});
