import * as os from 'os';
import dotenv from 'dotenv';
import { cleanEnv, str } from 'envalid';
import { megaBytes } from '../convertPartial';
import { positiveInteger } from '../convertToNumeric';
import { _coreReducerBody } from '../ensureCorrectCoreNumber';

dotenv.config();

describe('envalidConfig', () => {
  let envTEST: ReturnType<typeof cleanEnv>;
  beforeAll(() => {
    envTEST = cleanEnv(
      {},
      {
        MOCK_STR: str({ default: '' }),
        MOCK_NPM_PACKAGE_VERSION: str({
          default: process.env.npm_package_version
        }),
        MOCK_MEGABYTES: megaBytes({ default: 1 })
      }
    );
  });
  describe('cleanEnv', () => {
    it('should return empty string', () => {
      const testRelation = envTEST.MOCK_STR === '';
      expect(testRelation).toBe(true);
    });

    it('should return npm package version', () => {
      const testRelation =
        envTEST.MOCK_NPM_PACKAGE_VERSION === process.env.npm_package_version;
      expect(testRelation).toBe(true);
    });

    it('should return valid megaBytes multiplier', () => {
      const multiplier = 1024 * 1024;
      const testRelation = envTEST.MOCK_MEGABYTES === multiplier;
      expect(testRelation).toBe(true);
    });
  });

  describe('helper functions', () => {
    describe('integer', () => {
      it('should correctly check integer value', () => {
        const testRelation = positiveInteger('123') === 123;
        expect(testRelation).toBe(true);
      });

      it('should throw error', () => {
        expect(() => positiveInteger('abc')).toThrowError();
      });

      it('should throw error if value is not a number', () => {
        expect(() => positiveInteger('1e2')).toThrowError();
      });

      it('should throw error if value is negative', () => {
        expect(() => positiveInteger('-1')).toThrowError();
      });

      it('should throw error if value is decimal', () => {
        expect(() => positiveInteger('1.2')).toThrowError();
      });

      it('should throw error if value is empty', () => {
        expect(() => positiveInteger('')).toThrowError();
      });
    });

    describe(`coreReducer`, () => {
      it('should modify number of cores if input is below number of cores', () => {
        for (let i = -10; i < os.cpus().length; i++) {
          expect(_coreReducerBody(`${i}`)).toEqual(i);
        }
      });

      it('should return number 1 less than number of cores even if input is greater', () => {
        for (let i = os.cpus().length; i < os.cpus().length + 5; i++) {
          expect(_coreReducerBody(`${i}`)).toEqual(os.cpus().length - 1);
        }
      });
    });
  });
});
