import { Module } from '@nestjs/common';
import { RedisClientFactory } from './redis.client.factory';
import { RedisRepository } from './repository/redis.repository';
import { RedisService } from './redis.service';
import { LoggerModule } from '../logger/logger.module';

@Module({
  imports: [LoggerModule],
  providers: [RedisClientFactory, RedisRepository, RedisService],
  exports: [RedisService]
})
export class RedisModule {}
