export enum VersionEnum {
  v_1_0_0 = '1.0.0',
  v_1_0_1 = '1.0.1',
  v_1_1_0 = '1.1.0',
  v_1_1_1 = '1.1.1',
  v_1_1_2 = '1.1.2'
}

type literalZero = '0';
export type Id5CorrectFormat = `ID5*${string}`;
export type Id5ParamValue = literalZero | Id5CorrectFormat;

export type APIGetParams_v_1_0_0 = {
  version: VersionEnum;
  timestamp: number;
  ip: string;
  useragent: string;
  domain: string;
  serviceId: string;
  cms: string;
  wid: string;
  sid: string;
  id5: Id5ParamValue;

  uuid?: string;
  uuid_val?: string;
  fidState?: string;
  fid?: string;
  jid?: string;
  taid?: string;
  tpid?: string;
};

export type APIGetParams_v_1_1_0 = {
  accept_language?: string;
  cookie_enabled?: string;
  ctvid?: string;
  ctvid_type?: string;
  device_brand?: string;
  device_model?: string;
  idfv?: string;
  maid?: string;
  maid_type?: string;
  page_keywords?: string;
  page_referrer?: string;
  page_title?: string;
  page_url?: string;
  page_url_query_str?: string;
  timezone_offset?: string;
};

export type APIGetParams_v_1_1_1 = {
  syncid?: string;
  is3rdPartyCookie?: boolean;
};

export type APIGetParams_v_1_1_2 = {
  acceptLanguage?: string;
  cookiEnabled?: string;
  ctvidType?: string;
  deviceBrand?: string;
  deviceType?: string;
  deviceVersion?: string;
  maidType?: string;
  pageKeywords?: string;
  pageReferrer?: string;
  pageTitle?: string;
  pageUrl?: string;
  pageUrlQueryStr?: string;
  timezoneOffset?: string;
  wakePkg?: string;
  wakeVer?: string;
};

export type IHerringAPIGetParams = APIGetParams_v_1_0_0 &
  APIGetParams_v_1_1_0 &
  APIGetParams_v_1_1_1 &
  APIGetParams_v_1_1_2;
