import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Patch,
  Head,
  Req,
  HttpStatus
} from '@nestjs/common';

@Controller('/*')
export class NotFoundController {
  @Get()
  @Post()
  @Put()
  @Delete()
  @Patch()
  @Head()
  handleNotFound(@Req() req: Request): object {
    return {
      statusCode: HttpStatus.NOT_FOUND,
      message: `No route found for ${req.method} ${req.url}`
    };
  }
}
