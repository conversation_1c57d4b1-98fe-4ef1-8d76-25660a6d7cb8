import { Injectable } from '@nestjs/common';
import { LoggerService } from '../../logger/logger.service';
import { RedisService } from '../../redis/redis.service';
import { <PERSON><PERSON><PERSON><PERSON> } from '../../types';
import { LogInputs } from '../../types/logger.type';

@Injectable()
export class IdHelperService {
  constructor(
    private readonly redisService: RedisService,
    private readonly logger: LoggerService
  ) {
    this.logger.setContext(IdHelperService.name);
  }

  /**
   * Generate a new random ID - stringified 64-bit integer
   */
  generateNewRandomId(): string {
    const randomValues = new BigUint64Array(1);
    crypto.getRandomValues(randomValues);
    const generatedId = String(randomValues[0]);

    this.logger.log('GENERATE_NEW_RANDOM_ID', {
      id: generatedId
    });

    return generatedId;
  }

  /**
   * Get or create herring ID from storage
   */
  async getOrCreateHerringID(key: <PERSON><PERSON><PERSON><PERSON>, inputs: LogInputs): Promise<string> {
    let herringId = await this.redisService.get<string>(key);

    if (herringId) {
      this.logger.log('GET_HERRING_ID', {
        inputs,
        dbKey: key,
        dbValue: herringId
      });
    } else {
      herringId = this.generateNewRandomId();
      await this.redisService.set<string>(key, herringId);

      this.logger.log('CREATED_HERRING_ID', {
        inputs,
        dbKey: key,
        dbValue: herringId
      });
    }

    return herringId;
  }
}
