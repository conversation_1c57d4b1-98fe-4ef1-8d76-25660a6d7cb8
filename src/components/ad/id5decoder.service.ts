import { Injectable } from '@nestjs/common';
import { RedisService } from '../../redis/redis.service';
import { LoggerService } from '../../logger/logger.service';
import {
  ByteList,
  DecodedId5AndIdentity,
  KeyID5,
  PartnerBlock,
  Id5CorrectFormat,
  ValidKeysType
} from '../../types';

const ID5_PREFIX = 'ID5*' as const;

@Injectable()
export class ID5DecoderService {
  constructor(
    private readonly logger: LoggerService,
    private readonly redisService: RedisService
  ) {
    this.logger.setContext(ID5DecoderService.name);
  }

  public async decodeId5AndGetIdentity(
    id5: Id5CorrectFormat
  ): Promise<DecodedId5AndIdentity | null> {
    this.logger.log('DECODE_ID5_AND_GET_IDENTITY_START');
    const id5Uid = await this.decode(id5);

    if (!id5Uid) {
      this.logger.error('DECODE_ID5_AND_GET_IDENTITY_NO_ID5UID', { id5Uid });

      return null;
    }

    const redisKey: KeyID5 = `id5:${id5Uid}`;
    const identityId5 = await this.redisService.get<string>(redisKey);

    if (!identityId5) {
      this.logger.warn('DECODE_ID5_AND_GET_IDENTITY_NO_IDENTITY', { id5Uid });
      return null;
    }

    this.logger.log('DECODE_ID5_AND_GET_IDENTITY_END', { id5Uid, identity: identityId5 });
    return { id5Uid, identityId5 };
  }

  public async decode(encryptedId5Id: Id5CorrectFormat): Promise<string | null> {
    this.logger.log('DECODE_START', encryptedId5Id);

    const base64Part = encryptedId5Id.replace(ID5_PREFIX, '');

    try {
      const decoded = base64UrlDecodeToSigned(base64Part);

      const aBytes = decoded.subarray(0, 32);
      const partnersBlock = decoded.subarray(32);

      const key = await this._getKey(partnersBlock);

      if (!key) {
        this.logger.error('DECODE_NO_KEY', { encryptedId5Id });
        return null;
      }

      const fullKey = repeatToFillLength(key, aBytes.length);
      const uidBytes = xor(aBytes, fullKey);

      const decodedId5 = this._getId5Id(uidBytes);

      if (decodedId5.startsWith('ID5-UNRECOGNIZED')) {
        this.logger.error(
          'DECODE_ID5_UNRECOGNIZED',
          `Encrypted ID5 resulted in ID5-UNRECOGNIZED value. ID5 key: ${encryptedId5Id}`
        );
        return null;
      }

      this.logger.log('DECODE_END', decodedId5);

      return decodedId5;
    } catch (err) {
      this.logger.error(
        'DECODE_GENERAL_CATCH',
        `Cannot decode id5: ${encryptedId5Id} error msg: ${err}`
      );

      return null;
    }
  }

  private async _getKey(partnersBlock: Int8Array): Promise<Int8Array | null> {
    const blocks = this._splitPartnerBlock(partnersBlock, 16);

    const key = 'herring:current_id5_keys';
    const validKeys = await this.redisService.get<ValidKeysType>(key);

    if (!validKeys) {
      this.logger.warn('NO_VALID_KEYS', { key });
      return null;
    }

    for (const block of blocks) {
      const cBytes = block.subarray(2, 12);
      const dBytes = block.subarray(block.length - 4);

      const partnerKeyId = partnerKeyIdFromBlock(block);

      this.logger.debug('DECODE_PARTNER_KEY_ID', partnerKeyId);

      const partnerKeyEntry = validKeys[partnerKeyId];
      if (!partnerKeyEntry) {
        continue;
      }

      this.logger.log('DECODE_PARTNER_KEY_ENTRY_FROM_VALID_KEYS', {
        partnerKeyId,
        validKeysPartnerKeyEntry: partnerKeyEntry
      });

      // z_bytes = key1 XOR c_bytes
      const z_bytes = xorBytes(partnerKeyEntry.key1, cBytes);

      // crc 4B (signed, big-endian)
      const crc_bytes = crcOfBytesList(z_bytes);

      // key = d_bytes XOR (crc_bytes XOR key2)
      const inner = xorBytes(crc_bytes, partnerKeyEntry.key2);
      const key = xorBytes(dBytes, inner);

      return key;
    }

    this.logger.log('NO_KEY_FOUND');
    return null;
  }

  private _getId5Id(uid: Uint8Array): string {
    // First 4 bytes as ISO-8859-1 prefix
    const prefix = new TextDecoder('iso-8859-1').decode(uid.subarray(0, 4));
    // Remainder as Base64 suffix
    const suffix = base64UrlNoPad(uid.subarray(4));
    return `ID5-${prefix}${suffix}`;
  }

  private _splitPartnerBlock(partnersBlock: Int8Array, blockLength: number): Int8Array[] {
    if (partnersBlock.length % blockLength !== 0) {
      throw new Error(
        `partners block length ${partnersBlock.length} is not divisible by ${blockLength}`
      );
    }
    const result: Int8Array[] = [];
    for (let i = 0; i < partnersBlock.length; i += blockLength) {
      result.push(partnersBlock.subarray(i, i + blockLength));
    }
    return result;
  }
}

//TODO: przenieść do osobnego serwisu. START
const toUnsigned = (b: number) => b & 0xff;
const toSigned = (u: number) => ((u & 0xff) >= 0x80 ? (u & 0xff) - 0x100 : u & 0xff);

/** Base64URL bez paddingu ('=' usunięte) */
const base64UrlNoPad = (bytes: Uint8Array): string => {
  return Buffer.from(bytes)
    .toString('base64')
    .replace(/\+/g, '-')
    .replace(/\//g, '_')
    .replace(/=+$/g, ''); // usuwa '=' z końca
};

/** XOR dwóch list bajtów; wynik jako Int8Array (signed, -128..127) */
const xorBytes = (a: ByteList, b: ByteList): Int8Array => {
  if (a.length !== b.length) {
    throw new Error(`xorBytes: length mismatch a=${a.length} b=${b.length}`);
  }
  const len = a.length;
  const out = new Int8Array(len);
  for (let i = 0; i < len; i++) {
    out[i] = toSigned(toUnsigned(a[i]) ^ toUnsigned(b[i]));
  }
  return out;
};

/** CRC32 (zlib) dla bajtów; zwraca uint32 */
const crc32 = (() => {
  const createTable = (): Uint32Array => {
    const t = new Uint32Array(256);
    for (let i = 0; i < 256; i++) {
      let c = i;
      for (let j = 0; j < 8; j++) {
        c = c & 1 ? 0xedb88320 ^ (c >>> 1) : c >>> 1;
      }
      t[i] = c >>> 0;
    }
    return t;
  };

  const table = createTable();

  return (bytes: ByteList): number => {
    let c = 0xffffffff;
    for (let i = 0; i < bytes.length; i++) {
      const v = toUnsigned(bytes[i]);
      c = (c >>> 8) ^ table[(c ^ v) & 0xff];
    }
    return (c ^ 0xffffffff) >>> 0;
  };
})();

/** zwraca 4 bajty CRC jako signed [-128..127], big-endian */
const crcOfBytesList = (bytesValue: ByteList): Int8Array => {
  const crc = crc32(bytesValue);
  const b0 = (crc >>> 24) & 0xff;
  const b1 = (crc >>> 16) & 0xff;
  const b2 = (crc >>> 8) & 0xff;
  const b3 = (crc >>> 0) & 0xff;
  return new Int8Array([b0, b1, b2, b3].map(toSigned));
};

/** partner_key_id z pojedynczego 16-bajtowego bloku */
const partnerKeyIdFromBlock = (block: PartnerBlock): string => {
  if (block.length < 2) {
    throw new Error('partner block must be at least 2 bytes');
  }
  // big-endian (false = BE, true = LE)
  const view = new DataView(block.buffer, block.byteOffset, block.byteLength);
  const id = view.getUint16(0, false); // pierwsze 2 bajty, BE
  return String(id);
};

/** partner_key_id dla listy bloków */
const extractPartnerKeyIds = (blocks: PartnerBlock[]): string[] => {
  return blocks.map(partnerKeyIdFromBlock);
};

/**
 *  - dekoduje Base64URL (unpadded/padded),
 *  - zwraca signed bajty [-128..127] (jak _to_signed w Pythonie).
 */
const base64UrlDecodeToSigned = (value: string | Uint8Array): Int8Array => {
  let s =
    typeof value === 'string'
      ? value
      : Buffer.isBuffer(value)
        ? value.toString('utf8')
        : new TextDecoder().decode(value);

  s = s.trim().replace(/\s+/g, '');

  let buf: Buffer;
  try {
    buf = Buffer.from(s, 'base64url');
  } catch {
    let b64 = s.replace(/-/g, '+').replace(/_/g, '/');
    const pad = (4 - (b64.length % 4)) % 4; // 0..3
    if (pad) b64 += '='.repeat(pad);
    buf = Buffer.from(b64, 'base64');
  }

  return new Int8Array(buf.buffer, buf.byteOffset, buf.byteLength);
};

/**
 * Repeat a key buffer until it reaches the desired length.
 */
const repeatToFillLength = (key: Int8Array, length: number): Uint8Array => {
  const out = new Uint8Array(length);
  for (let i = 0; i < length; i++) {
    out[i] = key[i % key.length];
  }
  return out;
};

/**
 * XOR two equal-length Uint8Arrays.
 */
const xor = (a: Int8Array | Uint8Array, b: Int8Array | Uint8Array): Uint8Array => {
  if (a.length !== b.length) {
    throw new Error('Buffers must have the same length to XOR');
  }
  const result = new Uint8Array(a.length);
  for (let i = 0; i < a.length; i++) {
    result[i] = a[i] ^ b[i];
  }
  return result;
};
//TODO: przenieść do osobnego serwisu. END
