import Joi from 'joi';
import { validateId5Prefix } from '../herring/herring.schema';
import { Id5ParamValue } from '../../types';

export interface IAdSchema extends Record<string, string | boolean> {
  id5: Id5ParamValue;
  gdpr: '0' | '1';
  isDebug: boolean;
}

export const adSchema = Joi.object<IAdSchema>({
  id5: Joi.string()
    .min(1)
    .required()
    .custom(validateId5Prefix)
    .error((errors) => {
      errors.forEach((err) => {
        switch (err.code) {
          case 'any.required':
            err.message = 'ID5_IS_REQUIRED';
            break;
          case 'string.base':
            err.message = 'ID5_MUST_BE_STRING';
            break;
          case 'string.min':
            err.message = 'ID5_CANNOT_BE_EMPTY';
            break;
          case 'string.id5Prefix':
            err.message = 'ID5_INVALID_PREFIX';
            break;
          default:
            console.log('err.code', err.code);
            err.message = 'ID5_UNKNOWN_ERROR';
            break;
        }
      });
      return errors;
    }),
  gdpr: Joi.string()
    .valid('0', '1')
    .default('0')
    .error((errors) => {
      errors.forEach((err) => {
        switch (err.code) {
          case 'any.required':
            err.message = 'CONSENT_IS_REQUIRED';
            break;
          case 'string.base':
            err.message = 'CONSENT_MUST_BE_STRING';
            break;
          case 'any.only':
            err.message = 'CONSENT_MUST_BE_0_OR_1';
            break;
          default:
            console.log('err.code', err.code);
            err.message = 'CONSENT_UNKNOWN_ERROR';
            break;
        }
      });
      return errors;
    }), // consent
  isDebug: Joi.boolean().default(false).optional()
}).unknown(true);
