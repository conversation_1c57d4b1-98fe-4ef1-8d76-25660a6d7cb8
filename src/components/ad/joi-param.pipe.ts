import { PipeTransform } from '@nestjs/common';
import type <PERSON><PERSON><PERSON> from 'joi';
import { logger } from '../../logger';
import { RequestContextService } from '../../context/request-context.service';

export class JoiParamPipe<I, O> implements PipeTransform<I, O> {
  constructor(private readonly schema: JoiNS.ObjectSchema<O>) {}

  transform(value: I): O {
    const { error, value: v } = this.schema.validate(value, {
      abortEarly: false,
      convert: true,
      stripUnknown: false
    });
    if (error) {
      this.handleError(error);
    }
    return v;
  }

  private handleError(error: JoiNS.ValidationError) {
    const errMessages = error.details.map(({ message }) => message);

    logger.error(`VALIDATION`, { errDetails: errMessages.join(', ') });

    if (RequestContextService.isDebugMode()) {
      RequestContextService.addValidationErrors(errMessages);
    }
  }
}
