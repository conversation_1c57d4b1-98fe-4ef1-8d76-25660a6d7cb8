import { Test, TestingModule } from '@nestjs/testing';
import { RequestContext, RequestContextService } from '../../context/request-context.service';
import { adSchema, IAdSchema } from './ad.schema';
import { JoiParamPipe } from './joi-param.pipe';

jest.mock('../../logger/logger.service');

describe('Debug Integration Test', () => {
  let joiParamPipe: JoiParamPipe<any, any>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        {
          provide: JoiParamPipe,
          useValue: new JoiParamPipe(adSchema)
        }
      ]
    }).compile();

    joiParamPipe = module.get(JoiParamPipe);
  });

  it('should capture validation errors in debug context', () => {
    const context: RequestContext = {
      requestId: 'test-123',
      isDebugMode: true,
      collectedLogs: []
    };

    RequestContextService.runWithContext(context, () => {
      // Test with invalid data that should trigger validation errors
      const invalidData = {
        id5: 'invalid',
        gdpr: 'invalid',
        isDebug: true
      } as unknown as IAdSchema;

      try {
        joiParamPipe.transform(invalidData);
      } catch (error) {
        // Validation should fail, but we're testing error capture
      }

      // Check that validation errors were captured
      const errors = RequestContextService.getValidationErrors();
      expect(errors.length).toBeGreaterThan(0);

      expect(errors).toContain('ID5_INVALID_PREFIX');
      expect(errors).toContain('CONSENT_MUST_BE_0_OR_1');
    });
  });

  it('should not capture validation errors when not in debug mode', () => {
    const context: RequestContext = {
      requestId: 'test-123',
      isDebugMode: false,
      collectedLogs: []
    };

    RequestContextService.runWithContext(context, () => {
      const invalidData = {
        id5: '', // Empty string should fail validation
        gdpr: '2', // Invalid value should fail validation
        isDebug: false
      };

      try {
        joiParamPipe.transform(invalidData);
      } catch (error) {
        // Validation should fail, but errors shouldn't be captured
      }

      // Should not capture validation errors when not in debug mode
      const errors = RequestContextService.getValidationErrors();
      expect(errors).toEqual([]);
    });
  });

  it('should handle valid data without errors', () => {
    const context: RequestContext = {
      requestId: 'test-123',
      isDebugMode: true,
      collectedLogs: []
    };

    RequestContextService.runWithContext(context, () => {
      const validData = {
        id5: 'ID5*valid-id5-value',
        gdpr: '1',
        isDebug: true
      };

      const result = joiParamPipe.transform(validData);

      // Should transform successfully
      expect(result).toBeDefined();
      expect(result.id5).toBe('ID5*valid-id5-value');
      expect(result.gdpr).toBe('1');

      // Should not have any validation errors
      const errors = RequestContextService.getValidationErrors();
      expect(errors).toEqual([]);
    });
  });

  it('should work without context (graceful degradation)', () => {
    // Test outside of any context
    const invalidData = {
      id5: '',
      gdpr: '2'
    };

    try {
      joiParamPipe.transform(invalidData);
    } catch (error) {
      // Should still validate and fail, but not crash
    }

    // Should not crash when accessing context methods outside of context
    expect(RequestContextService.getValidationErrors()).toEqual([]);
    expect(RequestContextService.isDebugMode()).toBe(false);
  });
});
