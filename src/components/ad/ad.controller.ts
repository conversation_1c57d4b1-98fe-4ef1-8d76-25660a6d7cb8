import { Controller, Get, HttpStatus, Query, Redirect, UseInterceptors } from '@nestjs/common';
import { env } from '../../env/envalidConfig';
import { DebugResponseInterceptor } from '../../interceptors/debug-response.interceptor';
import { LoggerService } from '../../logger/logger.service';
import { URLParamsHelper } from '../../utils/urlHelper';
import { adSchema, IAdSchema } from './ad.schema';
import { AdService } from './ad.service';
import { JoiParamPipe } from './joi-param.pipe';

@Controller()
export class AdController {
  constructor(
    private readonly adService: AdService,
    private logger: LoggerService
  ) {
    this.logger.setContext(AdController.name);
  }

  private prepareRedirectUrl(
    baseUrl: string,
    query: Record<string, string | number | boolean>
  ): string {
    const url = new URLParamsHelper(baseUrl, '/');
    for (const [key, val] of Object.entries(query)) {
      url.add(key, val); // add new, if already exists, it will be overwritten
    }
    return url.toString();
  }

  //Przelotka
  @Get('ad.xml')
  @UseInterceptors(DebugResponseInterceptor)
  @Redirect(env.AD_SERVER_URL, HttpStatus.TEMPORARY_REDIRECT)
  async redirectAd(
    @Query(new JoiParamPipe(adSchema)) requestParams: IAdSchema
  ): Promise<{ url: string }> {
    this.logger.log('REQUEST_START', { requestParams });

    const { id5, gdpr } = requestParams;

    let redirectUrl = this.prepareRedirectUrl(env.AD_SERVER_URL, requestParams);

    const herringId = await this.adService.getForAdRequest(id5, gdpr, requestParams);
    if (herringId) {
      const { spratId: aouserid, herringId: ppid } = herringId;
      redirectUrl = this.prepareRedirectUrl(redirectUrl, { aouserid, ppid });
    }

    this.logger.log('REQUEST_END', { herringId, redirectUrl });

    return { url: redirectUrl };
  }
}
