import { Injectable } from '@nestjs/common';
import { LoggerService } from '../../logger/logger.service';
import { ID5DecoderService } from './id5decoder.service';
import { IAdSchema } from './ad.schema';
import {
  KeyCd,
  KeyId5UIDCs,
  DecodedId5AndIdentity,
  HerringIDType,
  IDGenerationEvent,
  IDGenerationEnum,
  HerringRandomID,
  HerringID,
  ProbXdIds
} from '../../types';
import { IdHelperService } from '../servicesWIthoutModule/idHelper.service';
import { Id5CorrectFormat, Id5ParamValue } from '../../types';

@Injectable()
export class AdService {
  constructor(
    private readonly id5DecoderService: ID5DecoderService,
    private readonly idHelperService: IdHelperService,
    private readonly logger: LoggerService
  ) {
    this.logger.setContext(AdService.name);
  }

  /**
   * Generates HerringID for ad requests based on ID5, consent, and other parameters
   * Processes a valid ad request and returns the HerringIDType or null
   * If the ID5 is not valid, it will return null
   * If the ID5 is valid, it will return the HerringIDType
   *
   * @param id5 - The ID5 to process
   * @param consent - The consent
   * @param requestParams - The request parameters
   * @returns The HerringIDType or null
   */
  async getForAdRequest(
    id5: Id5ParamValue,
    consent: '0' | '1',
    requestParams: IAdSchema
  ): Promise<HerringIDType | null> {
    const startTime = Date.now();
    this.logger.log('GET_FOR_AD_REQUEST_START', { startTime, id5, consent, requestParams });

    let ids: HerringIDType | null = null;
    let decodedIds: DecodedId5AndIdentity | null = null;

    const verifiedId5 = this.validateAdRequestInputs(id5, consent, requestParams);
    if (verifiedId5) {
      const manufacturedIds = await this.manufactureIds(verifiedId5);

      decodedIds = manufacturedIds.decodedIds;
      ids = manufacturedIds.ids;

      this.logger.log('IDS_MANUFACTURED', { ids, decodedIds });
    }

    const probXdIds: ProbXdIds = {};
    if (decodedIds) {
      probXdIds.id5 = { uid: decodedIds.id5Uid, id5Individual: decodedIds.identityId5 };
    }

    const logEvent: IDGenerationEvent = {
      createdAt: startTime,
      type: IDGenerationEnum.AD_REQUEST,
      modelInputs: {
        id5Uid: decodedIds?.id5Uid,
        identityId5: decodedIds?.identityId5
      },
      timeTakenMs: Date.now() - startTime,
      spratId: ids?.spratId,
      herringId: ids?.herringId,
      requestParams,
      probXdIds
    };

    this.logger.log('GET_FOR_AD_REQUEST_END', logEvent);

    return ids;
  }

  private validateAdRequestInputs(
    id5: Id5ParamValue,
    consent: '0' | '1',
    requestParams: IAdSchema
  ): Id5CorrectFormat | undefined {
    if ('aouserid' in requestParams) {
      this.logger.warn('FIELD_AOUSERID_EXISTS');
      return;
    }

    if (id5 === '0') {
      this.logger.warn('ID5_IS_0', { id5 });
      return;
    }

    if (consent !== '1') {
      this.logger.warn('CONSENT_IS_NOT_1', { consent });
      return;
    }

    return id5;
  }

  async manufactureIds(
    id5: Id5CorrectFormat
  ): Promise<{ ids: HerringIDType; decodedIds: DecodedId5AndIdentity | null }> {
    const decodedIds = await this.id5DecoderService.decodeId5AndGetIdentity(id5);

    let ids: HerringIDType;
    if (!decodedIds) {
      this.logger.warn('CANNOT_DECODE_ID5', { id5 });
      ids = this.createRandomIds();
    } else {
      this.logger.log('DECODED_ID5', decodedIds);
      ids = await this.getOrCreateConsistentIds(decodedIds);
    }
    return { ids, decodedIds };
  }

  createRandomIds(): HerringRandomID {
    return {
      spratId: `sr${this.idHelperService.generateNewRandomId().padStart(20, '0')}`,
      herringId: `hr${this.idHelperService.generateNewRandomId().padStart(20, '0')}`
    };
  }

  async getOrCreateConsistentIds(decodedIds: DecodedId5AndIdentity): Promise<HerringID> {
    const { id5Uid, identityId5 } = decodedIds;

    const herringCdKey: KeyCd = identityId5 ? `ind:${identityId5}` : `i5uid:${id5Uid}`;
    const herringCd = await this.idHelperService.getOrCreateHerringID(
      herringCdKey,
      decodedIds
    );

    const herringCsKey: KeyId5UIDCs = `id5uid:cs:${id5Uid}`;
    const herringCs = await this.idHelperService.getOrCreateHerringID(
      herringCsKey,
      decodedIds
    );

    return {
      spratId: `sp${herringCs.padStart(20, '0')}`,
      herringId: `he${herringCd.padStart(20, '0')}`
    };
  }
}
