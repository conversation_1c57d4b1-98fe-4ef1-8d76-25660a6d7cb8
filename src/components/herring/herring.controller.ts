import { Controller, Get, Query } from '@nestjs/common';
import { LoggerService } from '../../logger/logger.service';
import { HerringService } from './herring.service';
import { HerringVersionAwarePipe } from './herring-version-aware.pipe';
import { IHerringAPIGetParams, VersionEnum } from '../../types';

// TODO: Do ustawienia na datadog'u
// let REQUEST_HER_VER_COUNTER: Record<VersionEnum, number> = {
//   [VersionEnum.v_1_0_0]: 0,
//   [VersionEnum.v_1_0_1]: 0,
//   [VersionEnum.v_1_1_0]: 0,
//   [VersionEnum.v_1_1_1]: 0,
//   [VersionEnum.v_1_1_2]: 0
// };
// let REQUEST_HER_ID5_NO_CONSENT_COUNTER = 0;
// let REQUEST_HER_BAD_SCHEMA_COUNTER: Record<VersionEnum, number> = {
//   [VersionEnum.v_1_0_0]: 0,
//   [VersionEnum.v_1_0_1]: 0,
//   [VersionEnum.v_1_1_0]: 0,
//   [VersionEnum.v_1_1_1]: 0,
//   [VersionEnum.v_1_1_2]: 0
// };

type GetIdResponse = {
  request_version: string;
  request_uuid: string | undefined;
  time_taken: string;
  response_version: string;
  tidcdmn: string;
  tidcdvc: string;
};

@Controller()
export class HerringController {
  constructor(
    private readonly herringService: HerringService,
    private readonly logger: LoggerService
  ) {
    this.logger.setContext(HerringController.name);
  }

  @Get('get_id')
  async getId(
    @Query(new HerringVersionAwarePipe()) params: IHerringAPIGetParams
  ): Promise<GetIdResponse> {
    this.logger.log('GET_ID_REQUEST_START', { params });

    const { useragent, taid, tpid, sid, id5, wid, uuid, uuid_val, version } = params;
    const boltProfileIdToken = undefined; //TODO: Missing?

    const startTime = Date.now();

    if (id5 === '0') {
      this.logger.log('NO_CONSENT_FROM_USER', {
        inputParams: params,
        message: 'ID=0. No consent from the user.'
      });
      // REQUEST_HER_ID5_NO_CONSENT_COUNTER++;
    }

    const gottenIds = await this.herringService.get(
      useragent,
      taid,
      tpid,
      sid,
      id5,
      boltProfileIdToken,
      wid
    );

    const endTime = Date.now();
    const timeTaken = (endTime - startTime) / 1000;

    // REQUEST_HER_VER_COUNTER[version]++;

    let response_version: string;
    let tidcdmn: string;
    let tidcdvc: string;

    if (version >= VersionEnum.v_1_0_1) {
      const { csid, cdid } = gottenIds;
      const spratId = `sp${csid.padStart(20, '0')}`;
      const herringId = `he${cdid.padStart(20, '0')}`;

      tidcdmn = spratId;
      tidcdvc = herringId;
      response_version = '1.0.1';
    } else {
      tidcdmn = gottenIds.csid;
      tidcdvc = gottenIds.cdid;
      response_version = '1.0.0';
    }

    const response: GetIdResponse = {
      request_uuid: uuid || uuid_val,
      request_version: version,
      time_taken: timeTaken.toString(),
      response_version,
      tidcdmn,
      tidcdvc
    };

    this.logger.log('GET_ID_REQUEST_END', {
      response,
      timeTakenMs: endTime - startTime,
      herringId: gottenIds
    });

    return response;
  }
}
