import { <PERSON>du<PERSON> } from '@nestjs/common';
import { HerringController } from './herring.controller';
import { HerringService } from './herring.service';
import { RedisModule } from '../../redis/redis.module';
import { ID5DecoderService } from '../ad/id5decoder.service';
import { IdHelperService } from '../servicesWIthoutModule/idHelper.service';

@Module({
  imports: [RedisModule],
  controllers: [HerringController],
  providers: [HerringService, ID5DecoderService, IdHelperService]
})
export class HerringModule {}
