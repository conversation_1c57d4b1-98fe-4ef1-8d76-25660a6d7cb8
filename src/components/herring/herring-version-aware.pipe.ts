import { PipeTransform, BadRequestException, HttpException } from '@nestjs/common';
import { logger } from '../../logger';
import { getSchemaForVersion } from './herring.schema';
import { IHerringAPIGetParams, VersionEnum } from '../../types';
import Jo<PERSON> from 'joi';

export class HerringVersionAwarePipe implements PipeTransform<any, IHerringAPIGetParams> {
  transform(value: unknown): IHerringAPIGetParams {
    const versionSchema = Joi.object<{ version: VersionEnum }>({
      version: Joi.string()
        .default(VersionEnum.v_1_0_0)
        .valid(...Object.values(VersionEnum))
    });

    const { error: versionError, value: versionValue } = versionSchema.validate(value, {
      allowUnknown: true
    });

    if (versionError) {
      throw this.handleError(versionError);
    }

    const schema = getSchemaForVersion(versionValue.version);

    const { error, value: validatedValue } = schema.validate(value, {
      abortEarly: false,
      convert: true,
      stripUnknown: false
    });

    if (error) {
      throw this.handleError(error);
    }

    return validatedValue;
  }

  private handleError(error: Joi.ValidationError): HttpException {
    const errDetails = error.details.map((d) => ({ message: d.message, path: d.path }));
    logger.error('VALIDATION', { errDetails });

    return new BadRequestException({
      message: 'Validation failed',
      details: errDetails
    });
  }
}
