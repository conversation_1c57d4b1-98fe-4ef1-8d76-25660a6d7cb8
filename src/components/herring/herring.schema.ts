import Joi from 'joi';
import {
  APIGetParams_v_1_0_0,
  APIGetParams_v_1_1_0,
  APIGetParams_v_1_1_1,
  APIGetParams_v_1_1_2,
  Id5ParamValue,
  IHerringAPIGetParams,
  VersionEnum
} from '../../types';

export const validateId5Prefix = (
  value: string,
  helpers: Joi.CustomHelpers
): Id5ParamValue | Joi.ErrorReport => {
  if (value === '0' || value.startsWith('ID5*')) {
    return value as Id5ParamValue;
  }

  return helpers.error('string.id5Prefix');
};

export const herringAPIGetParamsSchemaV1_0_0AndV1_0_1 = Joi.object<APIGetParams_v_1_0_0>({
  id5: Joi.string()
    .min(1)
    .default('0')
    .custom(validateId5Prefix)
    .description('ID5 identifier'),
  uuid: Joi.string().description('Unique message id'),
  uuid_val: Joi.string().description('Unique message id'),
  version: Joi.string()
    .valid(...Object.values(VersionEnum))
    .required()
    .description('Version of the request'),
  timestamp: Joi.number().integer().required().description('Request timestamp'),
  ip: Joi.string().required().description('IP Address'),
  useragent: Joi.string().required().description('Useragent'),
  domain: Joi.string().required().description('Wake domain id'),
  serviceId: Joi.string().required().description('Wake service id'),
  cms: Joi.string().required().description('CMS'),
  wid: Joi.string().required().description('Wake id'),
  sid: Joi.string().required().description('SID'),
  fidState: Joi.string().description('FID State'),
  fid: Joi.string().description('FID'),
  jid: Joi.string().description('JID'),
  taid: Joi.string().description('TVN Account Id'),
  tpid: Joi.string().description('TVN Profile Id')
}).messages({
  'string.id5Prefix': 'The id5 field must start with "ID5*"'
});

const partialValidationObject_v_1_1_0: Joi.ObjectSchema<any> =
  Joi.object<APIGetParams_v_1_1_0>({
    accept_language: Joi.string().description('Accept-Language'),
    cookie_enabled: Joi.string().description('Cookie enabled'),
    ctvid: Joi.string().description('CTV ID'),
    ctvid_type: Joi.string().description('CTV ID type'),
    device_brand: Joi.string().description('Device brand'),
    device_model: Joi.string().description('Device model'),
    idfv: Joi.string().description('Apple ID for Vendors'),
    maid: Joi.string().description('Device identifier'),
    maid_type: Joi.string().description('Device identifier type'),
    page_keywords: Joi.string().description('Page keywords'),
    page_referrer: Joi.string().description('Page referrer'),
    page_title: Joi.string().description('Page title'),
    page_url: Joi.string().description('Page URL'),
    page_url_query_str: Joi.string().description('Page URL query string'),
    timezone_offset: Joi.string().description('Client timezone offset')
  });
export const herringAPIGetParamsSchemaV1_1_0 = herringAPIGetParamsSchemaV1_0_0AndV1_0_1.concat(
  partialValidationObject_v_1_1_0
);

const partialValidationObject_v_1_1_1: Joi.ObjectSchema<any> =
  Joi.object<APIGetParams_v_1_1_1>({
    syncid: Joi.string().description('Sync ID'),
    is3rdPartyCookie: Joi.boolean().description('Is 3rd party cookie')
  });

export const herringAPIGetParamsSchemaV1_1_1 = herringAPIGetParamsSchemaV1_1_0.concat(
  partialValidationObject_v_1_1_1
);

const partialValidationObject_v_1_1_2: Joi.ObjectSchema<any> =
  Joi.object<APIGetParams_v_1_1_2>({
    acceptLanguage: Joi.string().description('Accept-Language'),
    cookiEnabled: Joi.string().description('Cookie enabled'),
    ctvidType: Joi.string().description('CTV ID type'),
    deviceBrand: Joi.string().description('Device brand'),
    deviceType: Joi.string().description('Device type'),
    deviceVersion: Joi.string().description('Device version'),
    maidType: Joi.string().description('Device identifier type'),
    pageKeywords: Joi.string().description('Page keywords'),
    pageReferrer: Joi.string().description('Page referrer'),
    pageTitle: Joi.string().description('Page title'),
    pageUrl: Joi.string().description('Page URL'),
    pageUrlQueryStr: Joi.string().description('Page URL query string'),
    timezoneOffset: Joi.string().description('Client timezone offset'),
    wakePkg: Joi.string().description('Wake Package'),
    wakeVer: Joi.string().description('Wake Version')
  });

export const herringAPIGetParamsSchemaV1_1_2 = herringAPIGetParamsSchemaV1_1_1.concat(
  partialValidationObject_v_1_1_2
);

export const getSchemaForVersion = (
  version: VersionEnum
): Joi.ObjectSchema<IHerringAPIGetParams> => {
  switch (version) {
    case VersionEnum.v_1_1_2:
      return herringAPIGetParamsSchemaV1_1_2;

    case VersionEnum.v_1_1_1:
      return herringAPIGetParamsSchemaV1_1_1;

    case VersionEnum.v_1_1_0:
      return herringAPIGetParamsSchemaV1_1_0;

    default:
      return herringAPIGetParamsSchemaV1_0_0AndV1_0_1;
  }
};
