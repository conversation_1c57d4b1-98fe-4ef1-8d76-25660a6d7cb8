import { Injectable, NestInterceptor, ExecutionContext, CallHandler } from '@nestjs/common';
import { Observable } from 'rxjs';
import { tap } from 'rxjs/operators';
import { FastifyReply } from 'fastify';
import { RequestContextService } from '../context/request-context.service';
import { LoggerService } from '../logger/logger.service';

@Injectable()
export class DebugResponseInterceptor implements NestInterceptor {
  constructor(private logger: LoggerService) {
    this.logger.setContext(DebugResponseInterceptor.name);
  }

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const response = context.switchToHttp().getResponse<FastifyReply>();

    return next.handle().pipe(
      tap({
        next: () => {
          this.addDebugHeaders(response);
        },
        error: () => {
          this.addDebugHeaders(response);
        }
      })
    );
  }

  private addDebugHeaders(response: FastifyReply): void {
    try {
      if (RequestContextService.isDebugMode()) {
        const validationErrors = RequestContextService.getValidationErrors();

        if (validationErrors.length > 0) {
          const headerValue = validationErrors.join(',');
          response.header('x-tvn-debug-400', headerValue);
        }
      }
    } catch (error: any) {
      this.logger.error('DEBUG_HEADERS', error);
    }
  }
}
