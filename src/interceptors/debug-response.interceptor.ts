import { CallHandler, ExecutionContext, Injectable, NestInterceptor } from '@nestjs/common';
import { FastifyReply, FastifyRequest } from 'fastify';
import { Observable } from 'rxjs';
import { finalize, tap } from 'rxjs/operators';
import { RequestContextService } from '../context/request-context.service';
import { LoggerService } from '../logger/logger.service';

@Injectable()
export class DebugResponseInterceptor implements NestInterceptor {
  constructor(private logger: LoggerService) {
    this.logger.setContext(DebugResponseInterceptor.name);
  }

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const response = context.switchToHttp().getResponse<FastifyReply>();
    const request = context.switchToHttp().getRequest<FastifyRequest>();
    const requestId = (request as any).requestId;

    return next.handle().pipe(
      tap({
        next: () => {
          this.addDebugHeaders(response);
        },
        error: () => {
          this.addDebugHeaders(response);
        }
      }),
      finalize(() => {
        // Clean up context from cache after response is sent
        this.cleanupContext(requestId);
      })
    );
  }

  private addDebugHeaders(response: FastifyReply): void {
    try {
      if (RequestContextService.isDebugMode()) {
        const validationErrors = RequestContextService.getValidationErrors();

        if (validationErrors.length > 0) {
          const headerValue = validationErrors.join(',');
          response.header('x-tvn-debug-400', headerValue);
        }
      }
    } catch (error: any) {
      this.logger.error('DEBUG_HEADERS', error);
    }
  }

  private cleanupContext(requestId: string): void {
    try {
      if (requestId) {
        const cleaned = RequestContextService.cleanupContext(requestId);
        if (cleaned) {
          this.logger.debug('CONTEXT_CLEANUP', { requestId });
        }
        // Clear the current request ID reference
        RequestContextService.clearCurrentRequestId();
      }
    } catch (error: any) {
      this.logger.error('CONTEXT_CLEANUP_ERROR', { requestId, error });
    }
  }
}
