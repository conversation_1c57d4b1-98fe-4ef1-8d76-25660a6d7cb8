import { Injectable, OnM<PERSON>ule<PERSON><PERSON>roy } from '@nestjs/common';
import { RequestContext } from './request-context.service';

interface CachedContext {
  context: RequestContext;
  timestamp: number;
}

@Injectable()
export class RequestContextCache implements OnModuleD<PERSON>roy {
  private readonly cache = new Map<string, CachedContext>();
  private readonly TTL_MS = 5 * 60 * 1000; // 5 minutes
  private cleanupInterval: NodeJS.Timeout;

  constructor() {
    // Start periodic cleanup every minute
    this.cleanupInterval = setInterval(() => {
      this.cleanupExpiredContexts();
    }, 60 * 1000);
  }

  onModuleDestroy() {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }
    this.cache.clear();
  }

  /**
   * Store a request context in the cache
   */
  set(requestId: string, context: RequestContext): void {
    this.cache.set(requestId, {
      context,
      timestamp: Date.now()
    });
  }

  /**
   * Retrieve a request context from the cache
   */
  get(requestId: string): RequestContext | undefined {
    const cached = this.cache.get(requestId);
    
    if (!cached) {
      return undefined;
    }

    // Check if context has expired
    if (Date.now() - cached.timestamp > this.TTL_MS) {
      this.cache.delete(requestId);
      return undefined;
    }

    return cached.context;
  }

  /**
   * Remove a request context from the cache
   */
  delete(requestId: string): boolean {
    return this.cache.delete(requestId);
  }

  /**
   * Check if a request context exists in the cache
   */
  has(requestId: string): boolean {
    const cached = this.cache.get(requestId);
    
    if (!cached) {
      return false;
    }

    // Check if context has expired
    if (Date.now() - cached.timestamp > this.TTL_MS) {
      this.cache.delete(requestId);
      return false;
    }

    return true;
  }

  /**
   * Get the number of contexts currently in cache
   */
  size(): number {
    return this.cache.size;
  }

  /**
   * Clear all contexts from cache
   */
  clear(): void {
    this.cache.clear();
  }

  /**
   * Clean up expired contexts from cache
   */
  private cleanupExpiredContexts(): void {
    const now = Date.now();
    const expiredKeys: string[] = [];

    for (const [requestId, cached] of this.cache.entries()) {
      if (now - cached.timestamp > this.TTL_MS) {
        expiredKeys.push(requestId);
      }
    }

    for (const key of expiredKeys) {
      this.cache.delete(key);
    }

    if (expiredKeys.length > 0) {
      console.log(`RequestContextCache: Cleaned up ${expiredKeys.length} expired contexts`);
    }
  }

  /**
   * Get cache statistics for monitoring
   */
  getStats(): { size: number; oldestTimestamp: number | null } {
    let oldestTimestamp: number | null = null;

    for (const cached of this.cache.values()) {
      if (oldestTimestamp === null || cached.timestamp < oldestTimestamp) {
        oldestTimestamp = cached.timestamp;
      }
    }

    return {
      size: this.cache.size,
      oldestTimestamp
    };
  }
}
