import { Inject, Injectable, Optional, Scope } from '@nestjs/common';
import { REQUEST } from '@nestjs/core';
import { FastifyRequest } from 'fastify';
import { returnAsArray } from '../utils/returnAsArray';
import { RequestContextCache } from './request-context-cache.service';

export interface RequestContext {
  requestId: string;
  isDebugMode: boolean;
  collectedLogs: string[];
}

@Injectable({ scope: Scope.REQUEST })
export class RequestContextService {
  private static globalCache: RequestContextCache;
  private static currentRequestId: string | undefined;

  constructor(
    @Optional() @Inject(REQUEST) private readonly request: FastifyRequest,
    private readonly cache: RequestContextCache
  ) {
    // Store global cache reference for static methods
    RequestContextService.globalCache = this.cache;
  }

  /**
   * Initialize request context and store it in cache
   * For backward compatibility with existing code
   */
  static runWithContext<T>(context: RequestContext, callback: () => T): T {
    // Set the context in cache and current request ID
    RequestContextService.setContext(context);
    RequestContextService.currentRequestId = context.requestId;

    try {
      return callback();
    } finally {
      // Don't cleanup here - let the interceptor handle it
      // Just clear the current request ID reference
      RequestContextService.currentRequestId = undefined;
    }
  }

  /**
   * Set the current request context in cache
   */
  static setContext(context: RequestContext): void {
    if (RequestContextService.globalCache) {
      RequestContextService.globalCache.set(context.requestId, context);
    }
  }

  /**
   * Get current request context from cache
   */
  static getContext(): RequestContext | undefined {
    const requestId = RequestContextService.getCurrentRequestId();
    if (requestId && RequestContextService.globalCache) {
      return RequestContextService.globalCache.get(requestId);
    }
    return undefined;
  }

  /**
   * Get current request context (instance method)
   */
  getContext(): RequestContext | undefined {
    const requestId = this.getCurrentRequestId();
    if (requestId) {
      return this.cache.get(requestId);
    }
    return undefined;
  }

  /**
   * Set context in cache (instance method)
   */
  setContext(context: RequestContext): void {
    this.cache.set(context.requestId, context);
  }

  /**
   * Get current request ID from request object
   */
  private getCurrentRequestId(): string | undefined {
    return (this.request as any)?.requestId;
  }

  /**
   * Get current request ID (static method)
   */
  private static getCurrentRequestId(): string | undefined {
    // Return the current request ID set by runWithContext or middleware
    return RequestContextService.currentRequestId;
  }

  /**
   * Set current request ID for static method access
   */
  static setCurrentRequestId(requestId: string): void {
    RequestContextService.currentRequestId = requestId;
  }

  /**
   * Clear current request ID
   */
  static clearCurrentRequestId(): void {
    RequestContextService.currentRequestId = undefined;
  }

  static isDebugMode(): boolean {
    const context = this.getContext();
    return context?.isDebugMode ?? false;
  }

  static addValidationErrors(error: string | string[]): void {
    const context = this.getContext();
    if (context?.isDebugMode) {
      context.collectedLogs.push(...returnAsArray(error));
    }
  }

  static getValidationErrors(): string[] {
    const context = this.getContext();
    return context?.collectedLogs ?? [];
  }

  // for testing purposes only
  static clearValidationErrors(): void {
    const context = this.getContext();
    if (context) {
      context.collectedLogs = [];
    }
  }

  static getRequestId(): string | undefined {
    const context = this.getContext();
    return context?.requestId;
  }

  /**
   * Clean up context from cache
   */
  static cleanupContext(requestId: string): boolean {
    if (RequestContextService.globalCache) {
      return RequestContextService.globalCache.delete(requestId);
    }
    return false;
  }

  /**
   * Clean up context from cache (instance method)
   */
  cleanupContext(requestId?: string): boolean {
    const id = requestId || this.getCurrentRequestId();
    if (id) {
      return this.cache.delete(id);
    }
    return false;
  }
}
